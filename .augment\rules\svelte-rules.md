---
type: "always_apply"
---

## Svelte 5 Files (.svelte)
- Always use the latest Svelte 5 syntax and features.
- Always apply Tailwind 4 classes for styling and leverage utility classes when appropriate.
- Always reuse existing components from the repo whenever possible instead of creating new ones.
- Always use components from src/components/ui/* instead of raw HTML elements (eg. use Button component instead of <button>, Input instead of <input>, etc).
- Always use the '@' alias in import statements wherever possible, e.g., `import Component from '@/components/Component.svelte'`.
- Always use reactive declarations ($state(), $derived(), etc) appropriately for variables which change value.
- Never use reactive declarations for variables which never change state (static values).
- Always follow Svelte's guidelines for component composition and props.
- Always import types with the 'type' keyword in the import statement.

## TypeScript Files (.ts, .tsx)
- Always reuse existing type definitions from the codebase wherever possible.
- Always create appropriate type definitions or type aliases automatically  for new objects.
- Always prefer interfaces for object types that can be extended.
- Always use type aliases for unions, intersections, and primitive types.
- Always properly type function parameters and return values.
- Always leverage TypeScript's utility types when appropriate (Partial, Omit, Pick, etc.).
- Always avoid using 'any' type unless absolutely necessary, and if so, write a typescript waiver for that line of code.
- Always use proper typing for asynchronous code (Promises).