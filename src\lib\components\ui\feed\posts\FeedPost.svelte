<script lang="ts">
	import FeedPostHeader from './components/FeedPostHeader.svelte'
	import { type FeedPostT } from './types'
	import FeedPostContent from './components/FeedPostContent.svelte'
	import FeedPostFooter from './components/FeedPostFooter.svelte'
	import { onMount } from 'svelte'
	// import { redirect } from '@sveltejs/kit'
	import { goto } from '$app/navigation'
	import FeedPostReply from './components/FeedPostReply.svelte'

	let {
		id = '',
		userAvatarUrl = '/assistants/katie.png', // Default avatar
		username = 'Username',
		userId = '',
		dateCreated = '',
		title = '',
		content = '',
		replyCount = 0,
		tags = [],
		images = [],
		reactions = {},
		isCompact = true
	}: FeedPostT & { isCompact?: boolean } = $props()

	// // TODO: Need to add logic to get featured image!
	// const imageUrl = ''

	let postContainer: HTMLElement
	let isOverflowing = $state(false)
	let isHovering = $state(false)

	onMount(() => {
		if (isCompact) {
			checkOverflow()
		}
	})

	function checkOverflow() {
		if (postContainer) {
			isOverflowing = postContainer.scrollHeight > postContainer.clientHeight
		}
	}

	function navigateToPost() {
		if (isCompact && id) {
			goto(`/post/${id}`)
		}
	}
</script>

<div
	class="bg-muted border-border flex h-full w-full flex-col overflow-hidden rounded-lg border shadow-sm {isCompact
		? 'max-h-[600px]'
		: ''}"
>
	<FeedPostHeader {...{ userAvatarUrl, username, title, dateCreated, userId, id }}></FeedPostHeader>

	<!-- Content container with max height -->
	<div
		bind:this={postContainer}
		class="relative flex flex-1 flex-col overflow-hidden p-4 {isCompact ? 'cursor-pointer' : ''}"
		onclick={isCompact ? navigateToPost : undefined}
		onmouseenter={() => isCompact && (isHovering = true)}
		onmouseleave={() => isCompact && (isHovering = false)}
	>
		<!-- Image area -->

		<!-- <div class="w-full md:w-1/2"> -->
		<!-- <div class="w-full">
			{#if imageUrl}
				<img
					src={imageUrl}
					alt="Post image"
					class="h-auto max-h-48 w-full rounded-xl object-cover drop-shadow-lg"
				/>
			{:else}
				<div
					class="bg-muted flex h-32 w-full items-center justify-center rounded-xl drop-shadow-lg"
				>
					<span class="text-muted-foreground">No image</span>
				</div>
			{/if}
		</div> -->
		<!-- Description box -->
		<div class="flex w-full items-center p-4">
			<p class="text-card-foreground">
				<FeedPostContent {content, images} />
			</p>
		</div>

		<!-- Gradient overlay with "View Post" text when content overflows and isCompact is true -->
		{#if isCompact && isOverflowing}
			<div
				class="from-muted absolute right-0 bottom-0 left-0 flex h-42 items-end justify-center bg-gradient-to-t to-transparent pb-4 {isHovering
					? 'via-muted/60  text-blue-500'
					: 'text-primary  via-muted/80'}"
			>
				<span class="text-xl font-medium">View Post</span>
			</div>
		{/if}
	</div>

	<div class="mt-2 border-t p-4">
		<FeedPostFooter {...{ tags, replyCount, reactions }} />
	</div>
	{#if !isCompact}
		<div class="mt-4">
			<FeedPostReply {userId} />
		</div>
	{/if}
</div>
