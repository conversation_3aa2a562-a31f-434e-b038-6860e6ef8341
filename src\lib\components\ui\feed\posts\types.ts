import type { ImageApiT } from "@/lib/components/gallery/types"
import { ReactionsE, type ReactionsBarT } from "@/lib/components/ui/reactions/types"

export enum PostStatusE {
    New = 'new',
    Hot = 'hot',
    Top = 'top',
    DailyWinner = 'daily-winner'
}

export type FeedPostHeaderT = {
    userAvatarUrl: string,
    username: string,
    id: string,
    userId: string,
    dateCreated: string,
    title: string
}

export type FeedPostFooterT = {
    tags: string[],
    replyCount: number,
    reactions: ReactionsBarT,
}

export type FeedPostT = FeedPostHeaderT &
    FeedPostFooterT &
{ content: string }

export type FullPostT = FeedPostT & { replies: FeedPostT[] }

type _messageApiCommonT = {
    id: string,
    title: string,
    content: string,
    user_id: string,
    user_avatar_url: string,
    username: string,
    tags: string[],
    date_created: number,
    reactions: ReactionsBarT,
    images: ImageApiT[]
}
// Feed post format that the API returns
export type FeedPost_BACKEND_T = _messageApiCommonT & { num_replies: number }


export type FullPost_BACKEND_T = _messageApiCommonT & { replies: FullPost_BACKEND_T[] }


// export type BackendFeedPostT = {
//     id: string,
//     title: string,
//     content: string,
//     tags: string[],
//     date_created: string,
//     num_replies: number,
//     reactions: ReactionsBarT,
//     user_id: string,
//     username: string,
//     user_avatar_url: string
// }

// export type BackendFullPostT = BackendFeedPostT & { replies: BackendFeedPostT[] }
