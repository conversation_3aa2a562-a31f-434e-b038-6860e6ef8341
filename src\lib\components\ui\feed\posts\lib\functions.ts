import { getSmartTime } from "@/lib/helpers/time"
import type { FeedPost_BACKEND_T, FeedPostT, FullPost_BACKEND_T, FullPostT } from "../types"

export function parseBackendFeedMessage(backendMessage: FeedPost_BACKEND_T): FeedPostT {
    return {
        id: backendMessage.id,
        userAvatarUrl: backendMessage.user_avatar_url,
        username: backendMessage.username,
        userId: backendMessage.user_id,
        title: backendMessage.title,
        content: backendMessage.content,
        tags: backendMessage.tags,
        dateCreated: getSmartTime(backendMessage.date_created),
        replyCount: backendMessage.num_replies,
        reactions: backendMessage.reactions
    }
}

export function parseBackendFullPost(backendPost: FullPost_BACKEND_T): FullPostT {
    const retval: FullPostT = {
        id: backendPost.id,
        userAvatarUrl: backendPost.user_avatar_url,
        username: backendPost.username,
        userId: backendPost.user_id,
        dateCreated: getSmartTime(backendPost.date_created),
        title: backendPost.title,
        content: backendPost.content,
        tags: backendPost.tags,
        reactions: backendPost.reactions,
        replyCount: 0,
        replies: []
    }
    backendPost.replies.forEach((reply: FullPost_BACKEND_T) => {
        replies.push(parseBackendFullPost(reply))
    })
    const replies: FullPostT[] = []
    retval.replies = replies
    retval.replyCount = replies.length
    return retval
}