<script lang="ts">
	import type { PhotoSwipeItem, PhotoSwipeOptions, HashParams, GalleryImage } from './types'

	// ===== GALLERY DATA =====
	// const images = [
	// 	{ id: '-gS54SWrHMg', width: 1000, height: 500, caption: '1000x500' },
	// 	{ id: 'AU1rKyKPJco', width: 500, height: 1000, caption: '500x1000' },
	// 	{ id: 'AXfDvKOawZQ' },
	// 	{ id: 'gKlkZrsG_Pw', width: 1000, height: 500, caption: '1000x500' },
	// 	{ id: 'DVONaLgCRxo', width: 500, height: 1000, caption: '500x1000' },
	// 	{ id: 'o7txpYpxNLs', caption: 'Have a nice day' },
	// 	{ id: 'ZsgUsl8GATg', width: 500, height: 1000, caption: '500x1000' },
	// 	{ id: 'CkagyZJ88kE' },
	// 	{ id: 'PpQ4-HOZ_8U', width: 1000, height: 500, caption: '1000x500' },
	// 	{ id: 'si7gjqJQj_8' },
	// 	{ id: 'u0M0gyuexfE', width: 500, height: 1000, caption: '500x1000' },
	// 	{ id: 'aQcE3gDSSTY' },
	// 	{ id: 'GkCafprWKRo', width: 500, height: 1000, caption: '500x1000' },
	// 	{ id: 'OFlzoTfpRdw' },
	// 	{ id: 'YlFM0-LdHu8' },
	// 	{ id: 'c_Tc9ZELeYw' }
	// ]

	const images: GalleryImage[] = [
		{
			id: 'o7txpYpxNLs',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/awkward-penguin.jpg',
			caption: 'Have a nice day'
		},
		{
			id: 'OFlzoTfpRdw',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/blob'
		},
		{
			id: 'YlFM0-LdHu8',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/blueHammer.jpg'
		},
		{
			id: 'YlFM0-LdHfu8',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/clownFishCropped.jpg'
		},
		{
			id: 'c_Tc9ZELeasdfYw',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/metroid.png'
		},
		{
			id: 'c_Tc9ZELfaadfseYw',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/torchCoral.jpg'
		},
		{
			id: 'c_Tafssddsffc9ZELeYw',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/awkward-penguin.jpg'
		},
		{
			id: 'c_Tc9ZELfsdfeYw',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/awkward-penguin.jpg'
		},
		{
			id: 'c_Tsdfc9ZELeYw',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/awkward-penguin.jpg'
		},
		{
			id: 'c_Tc9ZEsdfsdfLeYw',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/awkward-penguin.jpg'
		},
		{
			id: 'c_dsfTc9ZELeYw',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/torchCoral.jpg'
		},
		{
			id: 'c_Tc9ZsdfELeYw',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/torchCoral.jpg'
		},
		{
			id: 'c_Tc9ZEffLeYw',
			url: 'https://reefdojo-img-bucket.s3.eu-north-1.amazonaws.com/users/2158f687-e086-481a-9124-51d8be81ec87/img/torchCoral.jpg'
		}
	]

	// ===== STATE =====
	let selectedImageIndex = $state(0)
	let selectedImage = $derived(images[selectedImageIndex])
	let primaryWidth = $derived(selectedImage.width || 500)
	let primaryHeight = $derived(selectedImage.height || 500)

	// ===== UTILITY FUNCTIONS =====
	const getImageClass = (width = 500, height = 500): string => {
		if (height > width) return 'vertical'
		if (width > height) return 'horizontal'
		return ''
	}

	const handleThumbnailSelect = (index: number) => {
		selectedImageIndex = index
	}

	const findClosest = (el: Element | null, predicate: (el: Element) => boolean): Element | null => {
		return el && (predicate(el) ? el : findClosest(el.parentNode as Element, predicate))
	}

	// ===== PHOTOSWIPE FUNCTIONS =====
	const parseThumbnailElements = (galleryElement: Element): PhotoSwipeItem[] => {
		const items: PhotoSwipeItem[] = []

		// Create items from the images array instead of parsing DOM
		for (let i = 0; i < images.length; i++) {
			const image = images[i]
			const width = (image.width || 500) * 2
			const height = (image.height || 500) * 2

			const item: PhotoSwipeItem = {
				src: `https://source.unsplash.com/${image.id}/${width}x${height}`,
				w: width,
				h: height,
				el: galleryElement // Use the container as the element reference
			}

			// Add caption if exists
			if (image.caption) {
				item.title = image.caption
			}

			// Add thumbnail source
			item.msrc = './hammers06.jpg'

			items.push(item)
		}

		return items
	}

	const parseHashParams = (): HashParams => {
		const hash = window.location.hash.substring(1)
		if (hash.length < 5) return {}

		const params: HashParams = {}
		const vars = hash.split('&')

		for (const variable of vars) {
			if (!variable) continue
			const [key, value] = variable.split('=')
			if (key && value) {
				params[key] = key === 'gid' ? parseInt(value, 10) : value
			}
		}

		return params
	}

	const getThumbBounds = (items: PhotoSwipeItem[], index: number) => {
		const thumbnail = items[index].el.getElementsByTagName('img')[0]
		const pageYScroll = window.pageYOffset || document.documentElement.scrollTop
		const rect = thumbnail.getBoundingClientRect()

		return {
			x: rect.left,
			y: rect.top + pageYScroll,
			w: rect.width
		}
	}

	const openPhotoSwipe = (
		index: number | string,
		galleryElement: Element,
		disableAnimation = false,
		fromURL = false
	): void => {
		const pswpElement = document.querySelector('.pswp')
		if (!pswpElement) return

		const items = parseThumbnailElements(galleryElement)
		const options: PhotoSwipeOptions = {
			galleryUID: galleryElement.getAttribute('data-pswp-uid'),
			getThumbBoundsFn: (idx: number) => getThumbBounds(items, idx)
		}

		// Set index based on source
		if (fromURL) {
			if (options.galleryPIDs) {
				const foundIndex = items.findIndex((item) => item.pid === index)
				options.index = foundIndex >= 0 ? foundIndex : 0
			} else {
				options.index = parseInt(index as string, 10) - 1
			}
		} else {
			options.index = parseInt(index as string, 10)
		}

		if (isNaN(options.index!) || options.index! < 0) return
		if (disableAnimation) options.showAnimationDuration = 0

		// Initialize PhotoSwipe
		const gallery = new (window as any).PhotoSwipe(
			pswpElement,
			(window as any).PhotoSwipeUI_Default,
			items,
			options
		)
		gallery.init()
	}

	const handlePrimaryImageClick = (e: Event): boolean => {
		e.preventDefault()

		// Open PhotoSwipe with all images, starting from the selected one
		const galleryElement = document.querySelector('.gallery-container')
		if (galleryElement) {
			openPhotoSwipe(selectedImageIndex, galleryElement)
		}

		return false
	}

	const handleThumbnailClick = (e: Event): boolean => {
		e.preventDefault()

		const clickedFigure = findClosest(
			e.target as Element,
			(el) => el.tagName?.toUpperCase() === 'FIGURE'
		)

		if (!clickedFigure?.parentNode) return false

		// Find index of clicked item
		const siblings = Array.from(clickedFigure.parentNode.children)
		const index = siblings.indexOf(clickedFigure)

		if (index >= 0) {
			openPhotoSwipe(index, clickedFigure.parentNode as Element)
		}

		return false
	}

	const initPhotoSwipe = (gallerySelector: string): void => {
		const galleryElements = document.querySelectorAll(gallerySelector)

		// Bind click events to thumbnail galleries only
		galleryElements.forEach((gallery, index) => {
			gallery.setAttribute('data-pswp-uid', (index + 1).toString())
			;(gallery as HTMLElement).onclick = handleThumbnailClick
		})

		// Handle URL hash navigation
		const hashData = parseHashParams()
		if (hashData.pid && hashData.gid && galleryElements[hashData.gid - 1]) {
			openPhotoSwipe(hashData.pid, galleryElements[hashData.gid - 1], true, true)
		}
	}

	// ===== INITIALIZATION =====
	initPhotoSwipe('.gallery')
</script>

<!-- Gallery HTML markup -->
<div class="gallery-container" itemscope itemtype="http://schema.org/ImageGallery">
	<!-- Primary Image Display -->
	<div class="primary-image-container">
		<figure
			class="primary-image-item"
			itemprop="associatedMedia"
			itemscope
			itemtype="http://schema.org/ImageObject"
		>
			<a
				href={selectedImage.url}
				itemprop="contentUrl"
				data-size="{primaryWidth * 2}x{primaryHeight * 2}"
				on:click={handlePrimaryImageClick}
			>
				<img
					class="lazyload lazypreload fadein"
					src={selectedImage.url}
					data-src={selectedImage.url}
					itemprop="thumbnail"
					alt="Image description"
				/>
			</a>
			<figcaption class="gallery-caption" itemprop="caption description">
				{selectedImage.caption || 'Caption'}
			</figcaption>
		</figure>
	</div>

	<!-- Thumbnail Gallery (hidden if only one image) -->
	{#if images.length > 1}
		<div class="gallery" itemscope itemtype="http://schema.org/ImageGallery">
			{#each images as image, index}
				{@const width = image.width || 500}
				{@const height = image.height || 500}
				{@const imageClass = getImageClass(width, height)}
				{@const isSelected = index === selectedImageIndex}
				<figure
					class="gallery-item {imageClass}"
					itemprop="associatedMedia"
					itemscope
					itemtype="http://schema.org/ImageObject"
				>
					<a
						href="https://source.unsplash.com/{image.id}/{width * 2}x{height * 2}"
						itemprop="contentUrl"
						data-size="{width * 2}x{height * 2}"
						class:selected={isSelected}
						on:click|preventDefault={() => handleThumbnailSelect(index)}
					>
						<img
							class="lazyload lazypreload fadein"
							src={image.url}
							data-src={image.url}
							itemprop="thumbnail"
							alt="Image description"
						/>
					</a>
					<figcaption class="gallery-caption" itemprop="caption description">
						{image.caption || 'Caption'}
					</figcaption>
				</figure>
			{/each}
		</div>
	{/if}
</div>

<!-- PhotoSwipe markup -->
<!-- Root element of PhotoSwipe. Must have class pswp. -->
<div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
	<!-- Background of PhotoSwipe. It's a separate element as animating opacity is faster than rgba(). -->
	<div class="pswp__bg"></div>

	<!-- Slides wrapper with overflow:hidden. -->
	<div class="pswp__scroll-wrap">
		<!-- Container that holds slides. PhotoSwipe keeps only 3 of them in the DOM to save memory. Don't modify these 3 pswp__item elements, data is added later on. -->
		<div class="pswp__container">
			<div class="pswp__item"></div>
			<div class="pswp__item"></div>
			<div class="pswp__item"></div>
		</div>

		<!-- Default (PhotoSwipeUI_Default) interface on top of sliding area. Can be changed. -->
		<div class="pswp__ui pswp__ui--hidden">
			<div class="pswp__top-bar">
				<!-- Controls are self-explanatory. Order can be changed. -->
				<div class="pswp__counter"></div>
				<button class="pswp__button pswp__button--close" title="Close (Esc)"></button>
				<button class="pswp__button pswp__button--share" title="Share"></button>
				<button class="pswp__button pswp__button--fs" title="Toggle fullscreen"></button>
				<button class="pswp__button pswp__button--zoom" title="Zoom in/out"></button>

				<!-- Preloader demo http://codepen.io/dimsemenov/pen/yyBWoR -->
				<!-- element will get class pswp__preloader--active when preloader is running -->
				<div class="pswp__preloader">
					<div class="pswp__preloader__icn">
						<div class="pswp__preloader__cut">
							<div class="pswp__preloader__donut"></div>
						</div>
					</div>
				</div>
			</div>

			<div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
				<div class="pswp__share-tooltip"></div>
			</div>

			<button class="pswp__button pswp__button--arrow--left" title="Previous (arrow left)"></button>
			<button class="pswp__button pswp__button--arrow--right" title="Next (arrow right)"></button>

			<div class="pswp__caption">
				<div class="pswp__caption__center"></div>
			</div>
		</div>
	</div>
</div>

<style>
	/* Gallery Caption Styles */
	.gallery-caption {
		position: absolute;
		bottom: 4rem;
		left: 50%;
		transform: translate(-50%, 0%);
		font-size: 12px;
		background: red;
		color: rgba(255, 255, 255, 0);
		padding: 1.25em 1.5em;
		transition: all 0.2s ease;
		font-weight: 600;
		line-height: 1.25;
		text-align: center;
		box-sizing: border-box;
		pointer-events: none;
	}

	@media (min-width: 768px) {
		.gallery-caption {
			font-size: 14px;
		}
	}

	.gallery-caption:before,
	.gallery-caption:after {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 1);
		width: 100%;
		height: 100%;
		transition: all 0.3s ease 0s;
		z-index: -1;
	}

	.gallery-caption:before {
		top: auto;
		height: 3px;
		transform: scale(0, 1);
		transform-origin: bottom left;
		transition-delay: 0.6s;
	}

	.gallery-caption:after {
		transform: scale(1, 0);
		transform-origin: bottom;
		transition-delay: 0.3s;
	}

	.gallery-caption.visible {
		color: rgba(255, 255, 255, 1);
		text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
		transition: all 0.3s ease 0.3s;
	}

	.gallery-caption.visible:before {
		transform: scale(1, 1);
		transition-delay: 0s;
	}

	.gallery-caption.visible:after {
		transform: scale(1, 1);
	}

	.gallery-caption:empty {
		display: none;
	}

	/* Image Border Styles */
	.gallery-item a {
		position: relative;
	}

	.gallery-item a:before,
	.gallery-item a:after {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		border: 0 solid rgba(0, 0, 0, 0.1);
		transition: all 0.2s;
		will-change: border;
		z-index: 10;
	}

	.gallery-item a.active:before {
		border-width: 0.5rem;
	}

	.gallery-item a.active:after {
		border-width: 2px;
	}

	.gallery-item a:after {
		margin: 1rem;
		border: 2px solid rgba(255, 255, 255, 0.5);
		clip-path: polygon(
			0 calc(100% - 1rem),
			0 100%,
			1rem 100%,
			1rem 0,
			0 0,
			0 1rem,
			100% 1rem,
			100% 0,
			calc(100% - 1rem) 0,
			calc(100% - 1rem) 100%,
			100% 100%,
			100% calc(100% - 1rem)
		);
	}

	.gallery-item a:hover:after {
		transform: scale(0.9);
		border-color: rgba(255, 255, 255, 1);
	}

	/* Caption Outside Styles */
	.gallery-caption.outside {
		background-color: black;
		color: white;
		padding: 0.75em 1em;
		display: inline-block;
		text-align: left;
	}

	/* Gallery Container Styles */
	.gallery-container {
		font-family:
			-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
		width: 100%;
		display: flex;
		gap: 1.5rem;
		align-items: flex-start;
	}

	@media (max-width: 767px) {
		.gallery-container {
			flex-direction: column;
		}

		.primary-image-container {
			margin-bottom: 1rem;
		}
	}

	/* Primary Image Styles */
	.primary-image-container {
		flex: 2;
		min-width: 0;
	}

	.primary-image-item {
		position: relative;
		background-color: rgba(0, 0, 0, 0.5);
		overflow: hidden;
		border-radius: 12px;
		width: 100%;
		aspect-ratio: 1;
	}

	.primary-image-item img,
	.primary-image-item a {
		display: block;
		width: 100%;
		height: 100%;
	}

	.primary-image-item img {
		object-fit: cover;
		object-position: center;
		border-radius: 12px;
	}

	.primary-image-item a {
		min-height: 300px;
		padding: 1rem;
		box-sizing: border-box;
		border-radius: 12px;
	}

	/* Thumbnail Gallery Grid Styles */
	.gallery {
		flex: 1;
		min-width: 0;
		display: grid;
		grid-template-rows: flow;
		grid-auto-flow: dense;
		gap: 0.75rem;
	}

	@media (max-width: 767px) {
		.gallery {
			grid-template-columns: repeat(3, 1fr);
		}
	}

	@media (min-width: 768px) and (max-width: 1023px) {
		.gallery {
			grid-template-columns: repeat(2, 1fr);
		}
	}

	@media (min-width: 1024px) {
		.gallery {
			grid-template-columns: repeat(3, 1fr);
		}
	}

	/* Gallery Item Styles */
	.gallery-item {
		position: relative;
		background-color: rgba(0, 0, 0, 0.5);
		overflow: hidden;
		aspect-ratio: 1;
		border-radius: 12px;
		cursor: pointer;
		transition: transform 0.2s ease;
	}

	.gallery-item:hover {
		transform: scale(1.05);
	}

	.gallery-item img,
	.gallery-item a {
		display: block;
	}

	.gallery-item img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		object-position: center;
		border-radius: 12px;
	}

	.gallery-item a {
		width: 100%;
		height: 100%;
		min-height: 120px;
		padding: 0.5rem;
		box-sizing: border-box;
		border-radius: 12px;
	}

	/* Selected thumbnail styles with fuchsia border */
	.gallery-item a.selected:after {
		border-color: rgb(217, 70, 239) !important; /* fuchsia-500 */
		border-width: 3px !important;
		transform: scale(0.95) !important;
	}

	.gallery-item a.selected:before {
		border-color: rgba(217, 70, 239, 0.3) !important;
		border-width: 1rem !important;
	}

	/* Lazy loading styles */
	.lazy-images .gallery-item a.image-lazyloaded:before,
	html:not(.lazy-images) .gallery-item a:before {
		border-width: 0.5rem;
	}

	.lazy-images .gallery-item a.image-lazyloaded:after,
	html:not(.lazy-images) .gallery-item a:after {
		border-width: 2px;
	}

	/* Caption visibility for non-touch devices and hover states */

	.gallery-item:hover .gallery-caption {
		color: rgba(255, 255, 255, 1);
		text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
		transition: all 0.3s ease 0.3s;
	}

	.gallery-item:hover .gallery-caption:before {
		transform: scale(1, 1);
		transition-delay: 0s;
	}

	.gallery-item:hover .gallery-caption:after {
		transform: scale(1, 1);
	}

	/* Hide captions in list views */
	[class*='list'] .gallery-caption,
	.gallery-size-thumbnail .gallery-caption {
		display: none;
	}

	.pswp {
		display: none;
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		overflow: hidden;
		touch-action: none;
		z-index: 1500;
		-webkit-text-size-adjust: 100%;
		backface-visibility: hidden;
		outline: none;
	}

	.pswp * {
		box-sizing: border-box;
	}

	.pswp img {
		max-width: none;
	}

	.pswp--open {
		display: block;
	}

	.pswp__bg {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background: #000;
		opacity: 0;
		transform: translateZ(0);
		backface-visibility: hidden;
		will-change: opacity;
	}

	.pswp__scroll-wrap {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		overflow: hidden;
	}

	.pswp__container,
	.pswp__zoom-wrap {
		touch-action: none;
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
	}

	.pswp__container,
	.pswp__img {
		user-select: none;
		-webkit-tap-highlight-color: transparent;
		-webkit-touch-callout: none;
	}

	.pswp__item {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		overflow: hidden;
	}

	.pswp__img {
		position: absolute;
		width: auto;
		height: auto;
		top: 0;
		left: 0;
	}
</style>
