export type Image_BACKEND_T = {
    id: string,
    name: string,
    description: string,
    date_created: number,
    url: string
}

export type ImageT = {
    id: string,
    name: string,
    description: string,
    dateCreated: string,
    url: string
}

export interface PhotoSwipeItem {
    src: string
    w: number
    h: number
    title?: string
    msrc?: string
    el: Element
    pid?: string
}

export interface PhotoSwipeOptions {
    galleryUID: string | null
    getThumbBoundsFn: (index: number) => { x: number; y: number; w: number }
    index?: number
    galleryPIDs?: boolean
    showAnimationDuration?: number
}

export interface HashParams {
    gid?: number
    pid?: string
    [key: string]: string | number | undefined
}

export interface GalleryImage {
    id: string
    width?: number
    height?: number
    caption?: string
}